import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Download, ArrowRight, Trash2 } from 'lucide-react';
import JSZip from 'jszip';
import FileUploader from './FileUploader';
import ConversionOptions, { ConversionConfig } from './ConversionOptions';
import FilePreview from './FilePreview';
import ConvertedFilePreview from './ConvertedFilePreview';
import ConfirmationModal from './ConfirmationModal';
import { createPixelatedImage, convertImageToFormat } from '../utils/imageProcessing';
import { useHorizontalScroll } from '../hooks/useHorizontalScroll';

export default function Converter() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<string>('png');
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [convertedFiles, setConvertedFiles] = useState<File[]>([]);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [isRemovingAll, setIsRemovingAll] = useState<boolean>(false);
  const [showRemoveAllModal, setShowRemoveAllModal] = useState<boolean>(false);
  const [config, setConfig] = useState<ConversionConfig>({
    ico: { maxSize: 48, selectedSizes: [] },
    webp: { quality: 90 },
    svg: { preset: 'default', detail: 50, smoothing: 50 },
    png: { compression: 6, preserveTransparency: true, dithering: false, backgroundColor: '#FFFFFF' },
    jpg: { quality: 90, smoothing: 0, progressive: false, preserveExif: false }
  });

  // Horizontal scroll refs
  const filesScrollRef = useHorizontalScroll<HTMLDivElement>(files.length > 0);
  const convertedScrollRef = useHorizontalScroll<HTMLDivElement>(convertedFiles.length > 0);

  const handleRemoveAll = useCallback(() => {
    setShowRemoveAllModal(true);
  }, []);

  const handleConfirmRemoveAll = useCallback(() => {
    setShowRemoveAllModal(false);

    // Iniciar animação de fade-out
    setIsRemovingAll(true);

    // Aguardar animação antes de limpar
    setTimeout(() => {
      setFiles([]);
      setConvertedFiles([]);
      setCurrentStep(1);
      setIsRemovingAll(false);
    }, 300); // Duração da animação
  }, []);

  const handleCancelRemoveAll = useCallback(() => {
    setShowRemoveAllModal(false);
  }, []);

  // Keyboard support for remove all (Delete/Backspace when focused on container)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only trigger if we're in step 2 and have multiple files
      if (currentStep !== 2 || files.length < 2) return;

      // Check if the target is the files container or one of its children
      const target = event.target as HTMLElement;
      const isInFilesContainer = target.closest('.horizontal-scroll-container');

      if (isInFilesContainer && (event.key === 'Delete' || event.key === 'Backspace')) {
        // Prevent default behavior (like going back in browser)
        event.preventDefault();
        handleRemoveAll();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [currentStep, files.length, handleRemoveAll]);

  const handleFilesAccepted = useCallback((acceptedFiles: File[]) => {
    setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
    setCurrentStep(2);
  }, []);

  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    setConvertedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    if (files.length === 1) {
      setCurrentStep(1);
      setConvertedFiles([]);
    }
  }, [files.length]);

  const handleConvert = useCallback(async () => {
    if (!files.length) return;

    setIsConverting(true);

    try {
      // Get quality based on format and config
      const getQuality = () => {
        if (selectedFormat === 'webp') {
          const quality = config.webp?.quality ?? 90;
          return Math.max(0, Math.min(100, quality)) / 100;
        }
        if (selectedFormat === 'jpg' || selectedFormat === 'jpeg') {
          const quality = config.jpg?.quality ?? 90;
          return Math.max(0, Math.min(100, quality)) / 100;
        }
        return 0.9; // Default quality for other formats
      };

      const convertedBlobs = await Promise.all(
        files.map(file => convertImageToFormat(file, selectedFormat, getQuality(), config))
      );

      const convertedFiles = convertedBlobs.map((blob, index) => {
        const fileName = files[index].name.split('.')[0];

        // Use the actual MIME type from the blob, which is more accurate
        let mimeType = blob.type;

        // Fallback to format-based MIME type if blob doesn't have one
        if (!mimeType || mimeType === 'application/octet-stream') {
          switch (selectedFormat.toLowerCase()) {
            case 'ico':
              mimeType = 'image/x-icon';
              break;
            case 'svg':
              mimeType = 'image/svg+xml';
              break;
            case 'jpg':
            case 'jpeg':
              mimeType = 'image/jpeg';
              break;
            case 'webp':
              mimeType = 'image/webp';
              break;
            default:
              mimeType = 'image/png';
          }
        }

        return new File([blob], `${fileName}.${selectedFormat}`, {
          type: mimeType
        });
      });

      setConvertedFiles(convertedFiles);
      setCurrentStep(3);
    } catch (error) {
      console.error('Conversion error:', error);
      // You might want to show an error message to the user here
    } finally {
      setIsConverting(false);
    }
  }, [files, selectedFormat, config]);
  
  const handleDownload = useCallback((index: number) => {
    if (!convertedFiles[index]) return;

    const file = convertedFiles[index];
    const url = URL.createObjectURL(file);

    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const handleDownloadAll = useCallback(async () => {
    if (!convertedFiles.length) return;

    const zip = new JSZip();

    // Add all files to the zip
    convertedFiles.forEach((file) => {
      zip.file(file.name, file);
    });

    // Generate the zip file
    const content = await zip.generateAsync({ type: 'blob' });

    // Create download link
    const url = URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `imagens_convertidas.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const resetConverter = () => {
    setFiles([]);
    setConvertedFiles([]);
    setCurrentStep(1);
  };

  return (
    <div className="space-y-3">
      {/* Header - Compacto */}
      <div className="card card-compact text-center">
        <h1 className="text-xl font-bold text-primary-900 mb-1">
          {t('hero.title')}
        </h1>
        <p className="text-xs text-primary-700">
          {t('hero.subtitle')}
        </p>
      </div>

      {/* Step Indicator - Compacto */}
      <div className="flex items-center justify-center space-x-3 mb-3">
        <div className={`flex items-center space-x-1.5 ${currentStep >= 1 ? 'text-accent-500' : 'text-primary-500'}`}>
          <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${currentStep >= 1 ? 'bg-accent-500 text-dark-bg' : 'bg-primary-200 text-primary-600'}`}>
            1
          </div>
          <span className="text-xs font-medium">Upload</span>
        </div>
        <ArrowRight className="w-3 h-3 text-primary-500" />
        <div className={`flex items-center space-x-1.5 ${currentStep >= 2 ? 'text-accent-500' : 'text-primary-500'}`}>
          <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${currentStep >= 2 ? 'bg-accent-500 text-dark-bg' : 'bg-primary-200 text-primary-600'}`}>
            2
          </div>
          <span className="text-xs font-medium">Converter</span>
        </div>
        <ArrowRight className="w-3 h-3 text-primary-500" />
        <div className={`flex items-center space-x-1.5 ${currentStep >= 3 ? 'text-accent-500' : 'text-primary-500'}`}>
          <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${currentStep >= 3 ? 'bg-accent-500 text-dark-bg' : 'bg-primary-200 text-primary-600'}`}>
            3
          </div>
          <span className="text-xs font-medium">Download</span>
        </div>
      </div>

      {/* Step 1: Upload */}
      {currentStep === 1 && (
        <div className="card card-compact">
          <FileUploader onFilesAccepted={handleFilesAccepted} />
        </div>
      )}

      {/* Step 2: Select Format & Convert */}
      {currentStep === 2 && (
        <div className="space-y-3 animate-fade-in">
          <div className="card card-compact">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-base font-semibold text-primary-900">
                Arquivos Selecionados ({files.length})
              </h3>

              {/* Botão Remover Todos - só aparece com 2+ arquivos */}
              {files.length >= 2 && (
                <button
                  onClick={handleRemoveAll}
                  disabled={isRemovingAll}
                  className="btn btn-outline btn-remove-all text-xs flex items-center gap-1.5 hover:text-red-400 hover:border-red-400 transition-colors"
                  aria-label={`Remover todos os ${files.length} arquivos selecionados`}
                  title="Remover todos os arquivos selecionados"
                >
                  <Trash2 className="w-3 h-3" />
                  <span className="hidden sm:inline">Remover Todos</span>
                </button>
              )}
            </div>

            <div
              className={`horizontal-scroll-container mb-4 transition-opacity duration-300 ${isRemovingAll ? 'opacity-0' : 'opacity-100'}`}
              tabIndex={files.length >= 2 ? 0 : -1}
              role="region"
              aria-label="Lista de arquivos selecionados. Pressione Delete ou Backspace para remover todos"
            >
              <div ref={filesScrollRef} className="horizontal-scroll-content">
                {files.map((file, index) => (
                  <div key={`${file.name}-${index}`} className="file-preview-item">
                    <FilePreview
                      file={file}
                      onRemove={() => handleRemoveFile(index)}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="card card-compact">
            <ConversionOptions
              selectedFormat={selectedFormat}
              onFormatChange={setSelectedFormat}
              config={config}
              onConfigChange={setConfig}
              files={files}
            />

            <div className="mt-4 flex justify-center space-x-3">
              <button
                className="btn btn-outline"
                onClick={resetConverter}
              >
                Voltar
              </button>
              <button
                className="btn btn-primary px-6"
                onClick={handleConvert}
                disabled={isConverting || files.length === 0}
              >
                {isConverting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t('converter.processing')}
                  </>
                ) : (
                  t('converter.convertButton')
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Download */}
      {currentStep === 3 && convertedFiles.length > 0 && (
        <div className="card card-compact animate-fade-in">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-base font-semibold text-primary-900">
              Arquivos Convertidos
            </h3>
            <div className="flex space-x-2">
              <button
                className="btn btn-outline text-xs"
                onClick={resetConverter}
              >
                Novo
              </button>
              {convertedFiles.length > 1 && (
                <button
                  onClick={handleDownloadAll}
                  className="btn btn-primary flex items-center gap-2 text-xs"
                >
                  <Download className="w-3 h-3" />
                  Baixar Todos
                </button>
              )}
            </div>
          </div>

          <div className="horizontal-scroll-container">
            <div ref={convertedScrollRef} className="horizontal-scroll-content">
              {convertedFiles.map((file, index) => (
                <div key={`converted-${file.name}-${index}`} className="file-preview-item">
                  <ConvertedFilePreview
                    originalFile={file}
                    convertedFormat={selectedFormat}
                    onDownload={() => handleDownload(index)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Modal de confirmação para remover todos */}
      <ConfirmationModal
        isOpen={showRemoveAllModal}
        onClose={handleCancelRemoveAll}
        onConfirm={handleConfirmRemoveAll}
        title="Remover Todos os Arquivos"
        message={`Tem certeza que deseja remover todos os ${files.length} arquivos selecionados? Esta ação não pode ser desfeita.`}
        confirmText="Remover Todos"
        cancelText="Cancelar"
        confirmVariant="danger"
      />
    </div>
  );
}