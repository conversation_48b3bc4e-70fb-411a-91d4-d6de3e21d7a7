import { useState, useMemo, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, Settings, Info } from 'lucide-react';
import { cn } from '../utils/cn';

interface FormatOption {
  id: string;
  icon: string;
}

export interface ConversionConfig {
  ico?: {
    maxSize: number;
    selectedSizes?: number[];
  };
  webp?: {
    quality: number;
  };
  svg?: {
    preset: string;
    detail: number;
    smoothing: number;
  };
  png?: {
    compression: number;
    preserveTransparency: boolean;
    dithering: boolean;
    filter: 'none' | 'sub' | 'up' | 'average' | 'paeth';
  };
  jpg?: {
    quality: number;
    smoothing: number;
    progressive: boolean;
    preserveExif: boolean;
  };
}

interface ConversionOptionsProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  config: ConversionConfig;
  onConfigChange: (config: ConversionConfig) => void;
  files?: File[];
}

export default function ConversionOptions({
  selectedFormat,
  onFormatChange,
  config,
  onConfigChange,
  files = []
}: ConversionOptionsProps) {
  const { t } = useTranslation();
  const [estimatedSize, setEstimatedSize] = useState<string>('');

  const formats = useMemo<FormatOption[]>(() => [
    { id: 'ico', icon: '🖼️' },
    { id: 'png', icon: '🎨' },
    { id: 'jpg', icon: '📷' },
    { id: 'webp', icon: '🌐' },
    { id: 'svg', icon: '✨' },
  ], []);

  const icoSizes = [16, 32, 48, 64, 128, 256];

  const getIncludedIcoSizes = (maxSize: number, selectedSizes?: number[]) => {
    if (selectedSizes && selectedSizes.length > 0) {
      // Use user-selected sizes
      return selectedSizes.sort((a, b) => a - b);
    }
    // Fallback: generate sizes up to maxSize (no forced base sizes)
    return icoSizes.filter(size => size <= maxSize).sort((a, b) => a - b);
  };

  // Estimate WebP file size based on quality
  const estimateWebPSize = (originalSize: number, quality: number) => {
    // Garantir que quality está no range válido
    const validQuality = Math.max(0, Math.min(100, quality));

    // Rough estimation: WebP typically reduces size significantly
    // Quality 0% = ~10% of original size, Quality 100% = ~70% of original size
    const compressionFactor = 0.1 + (validQuality / 100) * 0.6; // 0.1 to 0.7
    const estimatedSize = Math.round(originalSize * compressionFactor);

    // Garantir que o tamanho estimado não seja menor que 1% do original
    return Math.max(Math.round(originalSize * 0.01), estimatedSize);
  };

  // Estimate file size based on format and quality
  const estimateFileSize = (originalSize: number, format: string, config: ConversionConfig) => {
    switch (format) {
      case 'webp': {
        const quality = config.webp?.quality ?? 90;
        const compressionFactor = 0.1 + (quality / 100) * 0.6; // 0.1 to 0.7
        return Math.max(Math.round(originalSize * 0.01), Math.round(originalSize * compressionFactor));
      }
      case 'jpg': {
        const quality = config.jpg?.quality ?? 90;
        const compressionFactor = 0.2 + (quality / 100) * 0.6; // 0.2 to 0.8
        return Math.max(Math.round(originalSize * 0.02), Math.round(originalSize * compressionFactor));
      }
      case 'png': {
        const compression = config.png?.compression ?? 6;
        const dithering = config.png?.dithering ?? false;

        // PNG compression simulation - higher levels create smaller files
        let compressionFactor;
        if (compression <= 6) {
          compressionFactor = 0.85 - (compression / 6) * 0.15; // 0.85 to 0.7
        } else {
          // High compression levels (7-9) create significantly smaller files
          compressionFactor = 0.7 - ((compression - 6) / 3) * 0.3; // 0.7 to 0.4
        }

        // Dithering can slightly increase file size due to noise
        if (dithering) {
          compressionFactor *= 1.1;
        }

        return Math.round(originalSize * compressionFactor);
      }
      default:
        return originalSize;
    }
  };

  // Update estimated size when format or quality changes
  useEffect(() => {
    if ((selectedFormat === 'webp' || selectedFormat === 'jpg' || selectedFormat === 'png') && files.length > 0) {
      const totalOriginalSize = files.reduce((sum, file) => sum + file.size, 0);
      const estimatedTotalSize = estimateFileSize(totalOriginalSize, selectedFormat, config);

      const formatSize = (bytes: number) => {
        if (bytes < 1024) return `${bytes} B`;
        if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
        return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
      };

      setEstimatedSize(`${formatSize(totalOriginalSize)} → ${formatSize(estimatedTotalSize)}`);
    } else {
      setEstimatedSize('');
    }
  }, [selectedFormat, config.webp?.quality, config.jpg?.quality, config.png?.compression, config.png?.dithering, files]);

  const updateIcoConfig = (maxSize: number) => {
    onConfigChange({
      ...config,
      ico: {
        maxSize,
        selectedSizes: config.ico?.selectedSizes // Preserve selected sizes
      }
    });
  };

  const toggleIcoSize = (size: number) => {
    const currentSizes = config.ico?.selectedSizes || [];
    const isSelected = currentSizes.includes(size);

    let newSizes: number[];
    if (isSelected) {
      // Remove this size and all larger sizes
      newSizes = currentSizes.filter(s => s < size);
    } else {
      // Add this size and all smaller sizes
      const allSmallerSizes = icoSizes.filter(s => s <= size);
      newSizes = [...new Set([...currentSizes, ...allSmallerSizes])].sort((a, b) => a - b);
    }

    onConfigChange({
      ...config,
      ico: {
        maxSize: config.ico?.maxSize || 48,
        selectedSizes: newSizes
      }
    });
  };

  const clearIcoSizes = () => {
    onConfigChange({
      ...config,
      ico: {
        maxSize: config.ico?.maxSize || 48,
        selectedSizes: []
      }
    });
  };

  const updateWebPConfig = useCallback((quality: number) => {
    // Garantir que o valor está dentro do range válido (0-100)
    const validQuality = Math.max(0, Math.min(100, quality));

    onConfigChange({
      ...config,
      webp: {
        ...config.webp,
        quality: validQuality
      }
    });
  }, [config, onConfigChange]);

  const updateSvgConfig = (updates: Partial<{ preset: string; detail: number; smoothing: number }>) => {
    onConfigChange({
      ...config,
      svg: {
        preset: 'default',
        detail: 50,
        smoothing: 50,
        ...config.svg,
        ...updates
      }
    });
  };

  const updatePngConfig = useCallback((updates: Partial<{
    compression: number;
    preserveTransparency: boolean;
    dithering: boolean;
    filter: 'none' | 'sub' | 'up' | 'average' | 'paeth';
  }>) => {
    onConfigChange({
      ...config,
      png: {
        compression: 6,
        preserveTransparency: true,
        dithering: false,
        filter: 'none',
        ...config.png,
        ...updates
      }
    });
  }, [config, onConfigChange]);

  const updateJpgConfig = useCallback((updates: Partial<{
    quality: number;
    smoothing: number;
    progressive: boolean;
    preserveExif: boolean;
  }>) => {
    onConfigChange({
      ...config,
      jpg: {
        quality: 90,
        smoothing: 0,
        progressive: false,
        preserveExif: false,
        ...config.jpg,
        ...updates
      }
    });
  }, [config, onConfigChange]);

  const resetToDefaults = () => {
    const defaultConfig: ConversionConfig = {
      ico: { maxSize: 48, selectedSizes: [] },
      webp: { quality: 90 },
      svg: { preset: 'default', detail: 50, smoothing: 50 },
      png: { compression: 6, preserveTransparency: true, dithering: false, filter: 'none' },
      jpg: { quality: 90, smoothing: 0, progressive: false, preserveExif: false }
    };
    onConfigChange(defaultConfig);
  };

  return (
    <div>
      <h3 className="text-base font-semibold text-primary-900 mb-3 text-center">
        {t('converter.selectFormat')}
      </h3>

      {/* Layout horizontal: formatos à esquerda, configurações à direita */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Coluna da esquerda: Seleção de formatos */}
        <div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2">
            {formats.map((format) => (
              <button
                key={format.id}
                className={cn(
                  "relative flex flex-col items-center justify-center p-2.5 rounded-lg border-2 transition-all duration-300 hover:scale-105",
                  selectedFormat === format.id
                    ? "border-accent-500 bg-accent-500/10 shadow-lg"
                    : "border-dark-border bg-dark-surface hover:border-accent-500 hover:bg-accent-500/5 hover:shadow-md"
                )}
                onClick={() => onFormatChange(format.id)}
              >
                <span className="text-xl mb-1">{format.icon}</span>
                <span className="text-xs font-semibold text-primary-800">
                  {t(`formats.${format.id}`)}
                </span>

                {selectedFormat === format.id && (
                  <div className="absolute -top-1 -right-1">
                    <CheckCircle className="w-3.5 h-3.5 text-accent-500 fill-dark-surface shadow-lg rounded-full" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Coluna da direita: Configurações avançadas */}
        <div className="min-h-[160px]">
          {(selectedFormat === 'ico' || selectedFormat === 'webp' || selectedFormat === 'svg' || selectedFormat === 'png' || selectedFormat === 'jpg') ? (
            <div className="h-full">
              {renderAdvancedSettings()}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full p-4 bg-dark-surface/30 rounded-lg border border-dark-border/50">
              <div className="text-center">
                <Settings className="w-6 h-6 text-primary-500 mx-auto mb-1" />
                <p className="text-xs text-primary-600">
                  Configurações avançadas disponíveis para todos os formatos
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  function renderAdvancedSettings() {
    // Advanced Configuration for ICO
    if (selectedFormat === 'ico') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Settings className="w-3.5 h-3.5 text-accent-500" />
              <h4 className="text-sm font-semibold text-primary-900">{t('converter.icoSettings')}</h4>
            </div>
            <button
              onClick={resetToDefaults}
              className="text-xs text-primary-600 hover:text-accent-400 transition-colors px-2 py-1 rounded border border-dark-border hover:border-accent-500/50"
              title={t('converter.resetToDefaultsTooltip')}
            >
              {t('converter.resetToDefaults')}
            </button>
          </div>

          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-primary-700">
                  Tamanhos de Ícone
                </label>
                <button
                  onClick={clearIcoSizes}
                  className="text-xs text-primary-600 hover:text-accent-400 transition-colors"
                >
                  Limpar
                </button>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-1.5">
                {icoSizes.map((size) => {
                  const isSelected = config.ico?.selectedSizes?.includes(size) || false;
                  return (
                    <button
                      key={size}
                      className={cn(
                        "px-2 py-1.5 text-xs rounded-md border transition-all duration-200",
                        isSelected
                          ? "border-accent-500 bg-accent-500/10 text-accent-400"
                          : "border-dark-border bg-dark-surface text-primary-700 hover:border-accent-500/50"
                      )}
                      onClick={() => toggleIcoSize(size)}
                    >
                      {size}×{size}
                    </button>
                  );
                })}
              </div>
              <p className="text-xs text-primary-600 mt-1.5">
                Clique para incluir tamanho + menores. Padrão: até 48×48.
              </p>
            </div>

            <div className="bg-dark-bg/50 rounded-md p-2.5">
              <div className="flex items-center gap-2 mb-1.5">
                <Info className="w-3 h-3 text-accent-500" />
                <span className="text-xs font-medium text-primary-800">Tamanhos incluídos</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {getIncludedIcoSizes(config.ico?.maxSize || 48, config.ico?.selectedSizes).map((size) => (
                  <span
                    key={size}
                    className="px-1.5 py-0.5 text-xs bg-accent-500/10 text-accent-400 rounded border border-accent-500/20"
                  >
                    {size}×{size}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Advanced Configuration for WebP
    if (selectedFormat === 'webp') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border h-full">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Settings className="w-3.5 h-3.5 text-accent-500" />
              <h4 className="text-sm font-semibold text-primary-900">{t('converter.webpSettings')}</h4>
            </div>
            <button
              onClick={resetToDefaults}
              className="text-xs text-primary-600 hover:text-accent-400 transition-colors px-2 py-1 rounded border border-dark-border hover:border-accent-500/50"
              title={t('converter.resetToDefaultsTooltip')}
            >
              {t('converter.resetToDefaults')}
            </button>
          </div>

          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-primary-700">
                  {t('converter.quality')}
                </label>
                <span className="text-xs font-mono text-accent-400">
                  {config.webp?.quality ?? 90}%
                </span>
              </div>
              <input
                key="webp-quality-slider"
                type="range"
                min="0"
                max="100"
                step="1"
                value={config.webp?.quality ?? 90}
                onChange={(e) => {
                  const value = e.target.value;
                  const numValue = parseInt(value, 10);
                  if (!isNaN(numValue)) {
                    updateWebPConfig(numValue);
                  }
                }}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>{t('converter.lowerSize')}</span>
                <span>{t('converter.betterQuality')}</span>
              </div>
            </div>

            {estimatedSize && (
              <div className="bg-dark-bg/50 rounded-md p-2.5">
                <div className="flex items-center gap-2 mb-1">
                  <Info className="w-3 h-3 text-accent-500" />
                  <span className="text-xs font-medium text-primary-800">{t('converter.sizeEstimate')}</span>
                </div>
                <p className="text-sm font-mono text-accent-400">{estimatedSize}</p>
                <p className="text-xs text-primary-600 mt-1">
                  {t('converter.sizeEstimateNote')}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Advanced Configuration for SVG
    if (selectedFormat === 'svg') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Settings className="w-3.5 h-3.5 text-accent-500" />
              <h4 className="text-sm font-semibold text-primary-900">Configurações SVG</h4>
            </div>
            <button
              onClick={resetToDefaults}
              className="text-xs text-primary-600 hover:text-accent-400 transition-colors px-2 py-1 rounded border border-dark-border hover:border-accent-500/50"
              title={t('converter.resetToDefaultsTooltip')}
            >
              {t('converter.resetToDefaults')}
            </button>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-primary-700 mb-2">
                Preset de Vetorização
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1.5">
                {['default', 'posterized2', 'detailed', 'curvy'].map((preset) => (
                  <button
                    key={preset}
                    className={cn(
                      "px-2 py-1.5 text-xs rounded-md border transition-all duration-200 capitalize",
                      (config.svg?.preset || 'default') === preset
                        ? "border-accent-500 bg-accent-500/10 text-accent-400"
                        : "border-dark-border bg-dark-surface text-primary-700 hover:border-accent-500/50"
                    )}
                    onClick={() => updateSvgConfig({ preset })}
                  >
                    {preset === 'default' ? 'Padrão' :
                     preset === 'posterized2' ? 'Simples' :
                     preset === 'detailed' ? 'Detalhado' :
                     preset === 'curvy' ? 'Suave' : preset}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-primary-700">
                  Nível de Detalhe
                </label>
                <span className="text-xs font-mono text-accent-400">
                  {config.svg?.detail || 50}%
                </span>
              </div>
              <input
                type="range"
                min="10"
                max="100"
                value={config.svg?.detail || 50}
                onChange={(e) => updateSvgConfig({ detail: parseInt(e.target.value) })}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>Menos</span>
                <span>Mais</span>
              </div>
            </div>

            <div className="bg-dark-bg/50 rounded-md p-2.5">
              <div className="flex items-center gap-2 mb-1">
                <Info className="w-3 h-3 text-accent-500" />
                <span className="text-xs font-medium text-primary-800">Sobre SVG</span>
              </div>
              <p className="text-xs text-primary-600">
                Converte imagens em gráficos vetoriais escaláveis. Melhor para ícones e ilustrações.
              </p>
            </div>
          </div>
        </div>
      );
    }

    // Advanced Configuration for PNG
    if (selectedFormat === 'png') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Settings className="w-3.5 h-3.5 text-accent-500" />
              <h4 className="text-sm font-semibold text-primary-900">{t('converter.pngSettings')}</h4>
            </div>
            <button
              onClick={resetToDefaults}
              className="text-xs text-primary-600 hover:text-accent-400 transition-colors px-2 py-1 rounded border border-dark-border hover:border-accent-500/50"
              title={t('converter.resetToDefaultsTooltip')}
            >
              {t('converter.resetToDefaults')}
            </button>
          </div>

          <div className="space-y-3">
            {/* Compression Level */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-1">
                  <label className="text-xs font-medium text-primary-700">
                    {t('converter.compression')}
                  </label>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.compressionTooltip')}
                    </div>
                  </div>
                </div>
                <span className="text-xs font-mono text-accent-400">
                  {config.png?.compression ?? 6}
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="9"
                step="1"
                value={config.png?.compression ?? 6}
                onChange={(e) => updatePngConfig({ compression: parseInt(e.target.value) })}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>{t('converter.lessCompression')}</span>
                <span>{t('converter.moreCompression')}</span>
              </div>
            </div>

            {/* PNG Filter */}
            <div>
              <div className="flex items-center gap-1 mb-2">
                <label className="text-xs font-medium text-primary-700">
                  {t('converter.pngFilter')}
                </label>
                <div className="group relative">
                  <Info className="w-3 h-3 text-primary-500 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                    {t('converter.pngFilterTooltip')}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-1.5">
                {(['none', 'sub', 'up', 'average', 'paeth'] as const).map((filter) => (
                  <button
                    key={filter}
                    className={cn(
                      "px-2 py-1.5 text-xs rounded-md border transition-all duration-200 capitalize",
                      (config.png?.filter || 'none') === filter
                        ? "border-accent-500 bg-accent-500/10 text-accent-400"
                        : "border-dark-border bg-dark-surface text-primary-700 hover:border-accent-500/50"
                    )}
                    onClick={() => updatePngConfig({ filter })}
                  >
                    {filter}
                  </button>
                ))}
              </div>
            </div>

            {/* Toggle Options */}
            <div className="space-y-2">
              <label className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium text-primary-700">{t('converter.preserveTransparency')}</span>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.preserveTransparencyTooltip')}
                    </div>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.png?.preserveTransparency ?? true}
                  onChange={(e) => updatePngConfig({ preserveTransparency: e.target.checked })}
                  className="w-4 h-4 text-accent-500 bg-dark-surface border-dark-border rounded focus:ring-accent-500 focus:ring-2"
                />
              </label>
              <label className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium text-primary-700">{t('converter.dithering')}</span>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.ditheringTooltip')}
                    </div>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.png?.dithering ?? false}
                  onChange={(e) => updatePngConfig({ dithering: e.target.checked })}
                  className="w-4 h-4 text-accent-500 bg-dark-surface border-dark-border rounded focus:ring-accent-500 focus:ring-2"
                />
              </label>
            </div>

            {estimatedSize && (
              <div className="bg-dark-bg/50 rounded-md p-2.5">
                <div className="flex items-center gap-2 mb-1">
                  <Info className="w-3 h-3 text-accent-500" />
                  <span className="text-xs font-medium text-primary-800">{t('converter.sizeEstimate')}</span>
                </div>
                <p className="text-sm font-mono text-accent-400">{estimatedSize}</p>
                <p className="text-xs text-primary-600 mt-1">
                  {t('converter.sizeEstimateNote')}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Advanced Configuration for JPG
    if (selectedFormat === 'jpg') {
      return (
        <div className="p-3 bg-dark-surface/50 rounded-lg border border-dark-border animate-fade-in h-full">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Settings className="w-3.5 h-3.5 text-accent-500" />
              <h4 className="text-sm font-semibold text-primary-900">{t('converter.jpgSettings')}</h4>
            </div>
            <button
              onClick={resetToDefaults}
              className="text-xs text-primary-600 hover:text-accent-400 transition-colors px-2 py-1 rounded border border-dark-border hover:border-accent-500/50"
              title={t('converter.resetToDefaultsTooltip')}
            >
              {t('converter.resetToDefaults')}
            </button>
          </div>

          <div className="space-y-3">
            {/* Quality */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-1">
                  <label className="text-xs font-medium text-primary-700">
                    {t('converter.quality')}
                  </label>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.qualityTooltip')}
                    </div>
                  </div>
                </div>
                <span className="text-xs font-mono text-accent-400">
                  {config.jpg?.quality ?? 90}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                step="1"
                value={config.jpg?.quality ?? 90}
                onChange={(e) => updateJpgConfig({ quality: parseInt(e.target.value) })}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>{t('converter.lowerSize')}</span>
                <span>{t('converter.betterQuality')}</span>
              </div>
            </div>

            {/* Smoothing */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-1">
                  <label className="text-xs font-medium text-primary-700">
                    {t('converter.smoothing')}
                  </label>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.smoothingTooltip')}
                    </div>
                  </div>
                </div>
                <span className="text-xs font-mono text-accent-400">
                  {config.jpg?.smoothing ?? 0}
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                step="1"
                value={config.jpg?.smoothing ?? 0}
                onChange={(e) => updateJpgConfig({ smoothing: parseInt(e.target.value) })}
                className="w-full h-2 bg-dark-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-primary-600 mt-1">
                <span>{t('converter.lessSmoothing')}</span>
                <span>{t('converter.moreSmoothing')}</span>
              </div>
            </div>

            {/* Toggle Options */}
            <div className="space-y-2">
              <label className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium text-primary-700">{t('converter.progressive')}</span>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.progressiveTooltip')}
                    </div>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.jpg?.progressive ?? false}
                  onChange={(e) => updateJpgConfig({ progressive: e.target.checked })}
                  className="w-4 h-4 text-accent-500 bg-dark-surface border-dark-border rounded focus:ring-accent-500 focus:ring-2"
                />
              </label>
              <label className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium text-primary-700">{t('converter.preserveExif')}</span>
                  <div className="group relative">
                    <Info className="w-3 h-3 text-primary-500 cursor-help" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-dark-bg text-xs text-primary-200 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      {t('converter.preserveExifTooltip')}
                    </div>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.jpg?.preserveExif ?? false}
                  onChange={(e) => updateJpgConfig({ preserveExif: e.target.checked })}
                  className="w-4 h-4 text-accent-500 bg-dark-surface border-dark-border rounded focus:ring-accent-500 focus:ring-2"
                />
              </label>
            </div>

            {estimatedSize && (
              <div className="bg-dark-bg/50 rounded-md p-2.5">
                <div className="flex items-center gap-2 mb-1">
                  <Info className="w-3 h-3 text-accent-500" />
                  <span className="text-xs font-medium text-primary-800">{t('converter.sizeEstimate')}</span>
                </div>
                <p className="text-sm font-mono text-accent-400">{estimatedSize}</p>
                <p className="text-xs text-primary-600 mt-1">
                  {t('converter.sizeEstimateNote')}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    return null;
  }
}