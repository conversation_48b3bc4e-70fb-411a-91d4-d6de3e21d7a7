export default {
  header: {
    home: '<PERSON><PERSON><PERSON>',
    about: '<PERSON><PERSON>',
    contact: '<PERSON><PERSON><PERSON>',
    faq: 'FAQ',
  },
  hero: {
    title: 'Conversor de Imagens Online',
    subtitle: 'Converta facilmente suas imagens para vários formatos',
  },
  converter: {
    uploadTitle: 'Arraste e solte ou clique para fazer upload',
    uploadSubtitle: 'Suporta PNG, JPG, RAW e outros formatos (até 50MB)',
    dropzoneActive: 'Solte os arquivos aqui',
    convertButton: 'Converter',
    downloadButton: 'Baixar',
    selectFormat: 'Selecione o formato',
    processing: 'Processando...',
    noFiles: 'Nenhum arquivo selecionado',
    dragHere: 'Arraste seus arquivos aqui',
    orClick: 'ou clique para selecionar',
    dropToUpload: 'Solte para fazer upload',
    unsupportedFile: 'Arquivo não suportado. Formatos aceitos: PNG, JPG, RAW e outros.',
    icoSettings: 'Configurações ICO',
    webpSettings: 'Configurações WebP',
    pngSettings: 'Configurações PNG',
    jpgSettings: 'Configurações JPG',
    maxIconSize: 'Tamanho Máximo do Ícone',
    includedSizes: 'Tamanhos Incluídos',
    icoCompatibilityNote: 'O arquivo ICO sempre incluirá os tamanhos 16×16, 32×32 e 48×48 para máxima compatibilidade.',
    quality: 'Qualidade',
    compression: 'Compressão',
    smoothing: 'Suavização',
    preserveTransparency: 'Preservar Transparência',
    dithering: 'Dithering',
    pngFilter: 'Filtro PNG',
    progressive: 'JPG Progressivo',
    preserveExif: 'Preservar Metadados EXIF',
    lowerSize: 'Menor tamanho',
    betterQuality: 'Melhor qualidade',
    lessCompression: 'Menos compressão',
    moreCompression: 'Mais compressão',
    lessSmoothing: 'Menos suavização',
    moreSmoothing: 'Mais suavização',
    sizeEstimate: 'Estimativa de Tamanho',
    sizeEstimateNote: 'Estimativa baseada na qualidade selecionada. O tamanho real pode variar.',
    resetToDefaults: 'Padrão',
    resetToDefaultsTooltip: 'Restaurar configurações padrão',
    compressionTooltip: 'Nível de compressão PNG (0=sem compressão, 9=máxima compressão)',
    preserveTransparencyTooltip: 'Manter canal alfa/transparência da imagem original',
    ditheringTooltip: 'Aplicar dithering Floyd-Steinberg para efeito retrô',
    pngFilterTooltip: 'Filtro de predição PNG para otimizar compressão',
    qualityTooltip: 'Qualidade da imagem (0=menor tamanho, 100=melhor qualidade)',
    smoothingTooltip: 'Suavização/anti-aliasing (0=sem suavização, 100=máxima suavização)',
    progressiveTooltip: 'JPG progressivo carrega em camadas de qualidade crescente',
    preserveExifTooltip: 'Manter metadados EXIF da imagem original (localização, câmera, etc.)',
  },
  formats: {
    ico: 'Ícone (ICO)',
    png: 'PNG',
    jpg: 'JPEG',
    webp: 'WebP',
    svg: 'SVG',
    raw: 'RAW',
  },
  footer: {
    copyright: '© 2025 ZapFormat. Todos os direitos reservados.',
    terms: 'Termos de Uso',
    privacy: 'Política de Privacidade',
  },
};