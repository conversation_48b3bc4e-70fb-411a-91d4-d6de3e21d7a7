import { useEffect, useRef } from 'react';

/**
 * Custom hook for enhanced horizontal scrolling with wheel and touch support
 * @param enabled - Whether horizontal scrolling is enabled
 * @returns ref to attach to the scrollable element
 */
export function useHorizontalScroll<T extends HTMLElement>(enabled: boolean = true) {
  const scrollRef = useRef<T>(null);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element || !enabled) return;

    // Handle wheel events for horizontal scrolling
    const handleWheel = (e: WheelEvent) => {
      // Only handle horizontal scroll if there's overflow
      if (element.scrollWidth <= element.clientWidth) return;

      // Prevent default vertical scroll
      e.preventDefault();
      
      // Scroll horizontally based on wheel delta
      const scrollAmount = e.deltaY * 0.5; // Reduce sensitivity
      element.scrollLeft += scrollAmount;
    };

    // Handle touch events for mobile scrolling
    let touchStartX = 0;
    let touchStartScrollLeft = 0;
    let isScrolling = false;

    const handleTouchStart = (e: TouchEvent) => {
      if (element.scrollWidth <= element.clientWidth) return;
      
      touchStartX = e.touches[0].clientX;
      touchStartScrollLeft = element.scrollLeft;
      isScrolling = true;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isScrolling || element.scrollWidth <= element.clientWidth) return;
      
      e.preventDefault();
      const touchX = e.touches[0].clientX;
      const deltaX = touchStartX - touchX;
      element.scrollLeft = touchStartScrollLeft + deltaX;
    };

    const handleTouchEnd = () => {
      isScrolling = false;
    };

    // Add event listeners
    element.addEventListener('wheel', handleWheel, { passive: false });
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd);

    // Cleanup
    return () => {
      element.removeEventListener('wheel', handleWheel);
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enabled]);

  return scrollRef;
}
