@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-dark-bg text-primary-900 antialiased;
  }

  ::selection {
    @apply bg-accent-400 text-dark-bg;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-xs font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm;
  }

  .btn-primary {
    @apply bg-accent-500 text-dark-bg hover:bg-accent-400 hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-dark-surface text-primary-800 hover:bg-primary-100 border border-dark-border;
  }

  .btn-outline {
    @apply border border-dark-border bg-dark-surface hover:bg-primary-100 text-primary-800 hover:border-accent-500;
  }

  .container-custom {
    @apply px-4 mx-auto max-w-5xl sm:px-6 lg:px-8;
  }

  .drop-area {
    @apply border-2 border-dashed rounded-xl p-6 transition-all duration-300 ease-in-out relative overflow-hidden;
  }

  .drop-area:hover {
    @apply scale-[1.02] border-accent-400;
  }

  .drop-area-active {
    @apply border-accent-500 bg-accent-500/10 scale-105 shadow-2xl;
    animation: pulse-glow 2s ease-in-out infinite;
    background-image: linear-gradient(45deg, transparent 25%, rgba(0, 255, 128, 0.1) 25%, rgba(0, 255, 128, 0.1) 50%, transparent 50%, transparent 75%, rgba(0, 255, 128, 0.1) 75%);
    background-size: 20px 20px;
    animation: pulse-glow 2s ease-in-out infinite, moving-stripes 1s linear infinite;
  }

  .drop-area-reject {
    @apply border-red-500 bg-red-500/10;
    animation: shake 0.5s ease-in-out;
  }

  .drop-area-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 255, 128, 0.3);
    transform: scale(0);
    animation: ripple 1.5s ease-out infinite;
    pointer-events: none;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(0, 255, 128, 0.3), 0 0 40px rgba(0, 255, 128, 0.1);
      border-color: rgb(0, 255, 128);
    }
    50% {
      box-shadow: 0 0 30px rgba(0, 255, 128, 0.5), 0 0 60px rgba(0, 255, 128, 0.2);
      border-color: rgb(51, 255, 153);
    }
  }

  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }

  @keyframes rotate-pulse {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
  }

  @keyframes moving-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
  }

  .icon-upload {
    transition: all 0.3s ease-in-out;
  }

  .icon-upload-active {
    animation: rotate-pulse 2s ease-in-out infinite;
  }

  .thumbnail-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .card {
    @apply bg-dark-surface rounded-xl shadow-lg border border-dark-border;
  }

  .card-compact {
    @apply p-3;
  }

  .card-normal {
    @apply p-6;
  }

  /* Custom slider styles for WebP quality control */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #4ade80;
    cursor: pointer;
    border: 2px solid #1a1a1a;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-moz-range-thumb {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #4ade80;
    cursor: pointer;
    border: 2px solid #1a1a1a;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-webkit-slider-track {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #374151 0%, #4ade80 100%);
  }

  .slider::-moz-range-track {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #374151 0%, #4ade80 100%);
    border: none;
  }

  /* Horizontal scroll container for file previews */
  .horizontal-scroll-container {
    @apply w-full overflow-hidden;
    position: relative;
  }

  .horizontal-scroll-content {
    @apply flex gap-3 overflow-x-auto pb-2;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #00ff80 #111111;
    transition: all 0.3s ease-in-out;
  }

  /* Custom scrollbar for webkit browsers */
  .horizontal-scroll-content::-webkit-scrollbar {
    height: 8px;
  }

  .horizontal-scroll-content::-webkit-scrollbar-track {
    background: #111111;
    border-radius: 4px;
    margin: 0 8px;
  }

  .horizontal-scroll-content::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #00cc66, #00ff80);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  .horizontal-scroll-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, #00ff80, #33ff99);
  }

  /* File preview item styling */
  .file-preview-item {
    @apply flex-shrink-0;
    width: 140px;
    transition: transform 0.2s ease-in-out;
  }

  .file-preview-item:hover {
    transform: translateY(-2px);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .file-preview-item {
      width: 120px;
    }

    .horizontal-scroll-content {
      @apply gap-2;
    }
  }

  @media (max-width: 480px) {
    .file-preview-item {
      width: 100px;
    }
  }

  /* Smooth scroll animation */
  @keyframes smoothScroll {
    from {
      transform: translateX(10px);
      opacity: 0.8;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .file-preview-item {
    animation: smoothScroll 0.3s ease-out;
  }

  /* Enhanced hover effects for better UX */
  .horizontal-scroll-content:hover {
    scrollbar-color: #33ff99 #111111;
  }

  /* Focus styles for accessibility */
  .horizontal-scroll-content:focus-within {
    outline: 2px solid #00ff80;
    outline-offset: 2px;
    border-radius: 8px;
  }

  /* Focus styles for the container when using keyboard navigation */
  .horizontal-scroll-container:focus {
    outline: 2px solid #00ff80;
    outline-offset: 2px;
    border-radius: 8px;
    background-color: rgba(0, 255, 128, 0.05);
  }

  /* Visual hint for keyboard users */
  .horizontal-scroll-container:focus::before {
    content: 'Pressione Delete/Backspace para remover todos os arquivos';
    position: absolute;
    top: -25px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 10px;
    color: #00ff80;
    background: rgba(0, 0, 0, 0.8);
    padding: 2px 8px;
    border-radius: 4px;
    z-index: 20;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Scroll indicators for better UX */
  .horizontal-scroll-container::before,
  .horizontal-scroll-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 10;
    transition: opacity 0.3s ease;
  }

  .horizontal-scroll-container::before {
    left: 0;
    background: linear-gradient(90deg, #111111, transparent);
    opacity: 0;
  }

  .horizontal-scroll-container::after {
    right: 0;
    background: linear-gradient(270deg, #111111, transparent);
    opacity: 0;
  }

  /* Show scroll indicators when content overflows */
  .horizontal-scroll-container:hover::before,
  .horizontal-scroll-container:hover::after {
    opacity: 1;
  }

  /* Smooth momentum scrolling for iOS */
  .horizontal-scroll-content {
    -webkit-overflow-scrolling: touch;
  }

  /* Performance optimizations */
  .file-preview-item {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Prevent text selection during scroll */
  .horizontal-scroll-content {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Re-enable text selection for file names */
  .file-preview-item p {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Remove all button hover effects */
  .btn-outline:hover:has(.lucide-trash-2) {
    border-color: #ef4444;
    color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
  }

  /* Fade out animation for removing all files */
  .fade-out-files {
    animation: fadeOutFiles 0.3s ease-out forwards;
  }

  @keyframes fadeOutFiles {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.95);
    }
  }

  /* Enhanced button styles for remove all */
  .btn-remove-all {
    @apply transition-all duration-200 ease-in-out;
  }

  .btn-remove-all:hover {
    @apply shadow-md;
    transform: translateY(-1px);
  }

  .btn-remove-all:active {
    transform: translateY(0);
  }

  /* Disabled state for remove all button */
  .btn-remove-all:disabled {
    @apply opacity-50 cursor-not-allowed;
    transform: none !important;
  }

  /* Modal animations */
  .animate-slide-up {
    animation: slideUpModal 0.3s ease-out;
  }

  @keyframes slideUpModal {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Modal backdrop blur effect */
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  /* Danger button variant for modal */
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white shadow-sm;
    transition: all 0.2s ease-in-out;
  }

  .btn-danger:hover {
    @apply shadow-md;
    transform: translateY(-1px);
  }

  .btn-danger:active {
    transform: translateY(0);
  }

  /* Modal focus styles */
  .modal-focus-trap button:focus {
    @apply outline-none ring-2 ring-accent-500 ring-offset-2 ring-offset-dark-surface;
  }

  /* Enhanced modal card styling */
  .modal-card {
    @apply bg-dark-surface border border-dark-border shadow-2xl;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
  }
}