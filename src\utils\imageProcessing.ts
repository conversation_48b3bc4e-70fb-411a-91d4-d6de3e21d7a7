import { isRawFormat } from './fileValidation';
// @ts-ignore - imagetracerjs doesn't have TypeScript definitions
import ImageTracer from 'imagetracerjs';

/**
 * Apply <PERSON>-<PERSON> dithering to image data
 * This creates a retro/pixelated effect by reducing color depth with error diffusion
 */
function applyFloydSteinbergDithering(imageData: ImageData) {
  const data = imageData.data;
  const width = imageData.width;
  const height = imageData.height;

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;

      // Get original RGB values
      const oldR = data[idx];
      const oldG = data[idx + 1];
      const oldB = data[idx + 2];

      // Quantize to fewer colors (reduce to 8 levels per channel for more visible effect)
      const levels = 8; // Number of levels per channel
      const step = 255 / (levels - 1);
      const newR = Math.round(oldR / step) * step;
      const newG = Math.round(oldG / step) * step;
      const newB = Math.round(oldB / step) * step;

      // Set new values
      data[idx] = newR;
      data[idx + 1] = newG;
      data[idx + 2] = newB;

      // Calculate error
      const errR = oldR - newR;
      const errG = oldG - newG;
      const errB = oldB - newB;

      // Distribute error to neighboring pixels
      const distributeError = (dx: number, dy: number, factor: number) => {
        const nx = x + dx;
        const ny = y + dy;
        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
          const nIdx = (ny * width + nx) * 4;
          data[nIdx] = Math.max(0, Math.min(255, data[nIdx] + errR * factor));
          data[nIdx + 1] = Math.max(0, Math.min(255, data[nIdx + 1] + errG * factor));
          data[nIdx + 2] = Math.max(0, Math.min(255, data[nIdx + 2] + errB * factor));
        }
      };

      // Floyd-Steinberg error distribution pattern
      distributeError(1, 0, 7/16);  // Right
      distributeError(-1, 1, 3/16); // Bottom-left
      distributeError(0, 1, 5/16);  // Bottom
      distributeError(1, 1, 1/16);  // Bottom-right
    }
  }
}

/**
 * Apply color reduction to simulate PNG compression
 * Higher compression levels reduce color depth more aggressively
 */
function applyColorReduction(imageData: ImageData, compressionLevel: number) {
  const data = imageData.data;
  const width = imageData.width;
  const height = imageData.height;

  // Map compression level (0-9) to color reduction factor
  // Level 6 = no reduction (default), 7-9 = increasing reduction
  const reductionFactor = Math.max(1, compressionLevel - 5); // 1-4
  const colorLevels = Math.max(2, 8 - reductionFactor); // 2-7 color levels per channel
  const step = 255 / (colorLevels - 1);

  for (let i = 0; i < data.length; i += 4) {
    // Quantize RGB channels (preserve alpha)
    data[i] = Math.round(data[i] / step) * step;     // Red
    data[i + 1] = Math.round(data[i + 1] / step) * step; // Green
    data[i + 2] = Math.round(data[i + 2] / step) * step; // Blue
    // Alpha channel (data[i + 3]) remains unchanged
  }
}

/**
 * Apply progressive JPEG effect simulation
 * Progressive JPEGs load in multiple passes, creating a subtle visual difference
 */
function applyProgressiveJpegEffect(imageData: ImageData) {
  const data = imageData.data;
  const width = imageData.width;
  const height = imageData.height;

  // Apply a subtle sharpening effect that mimics progressive JPEG encoding
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;

      // Get surrounding pixels for sharpening kernel
      const center = [data[idx], data[idx + 1], data[idx + 2]];
      const top = [data[((y-1) * width + x) * 4], data[((y-1) * width + x) * 4 + 1], data[((y-1) * width + x) * 4 + 2]];
      const bottom = [data[((y+1) * width + x) * 4], data[((y+1) * width + x) * 4 + 1], data[((y+1) * width + x) * 4 + 2]];
      const left = [data[(y * width + (x-1)) * 4], data[(y * width + (x-1)) * 4 + 1], data[(y * width + (x-1)) * 4 + 2]];
      const right = [data[(y * width + (x+1)) * 4], data[(y * width + (x+1)) * 4 + 1], data[(y * width + (x+1)) * 4 + 2]];

      // Apply subtle sharpening (progressive JPEG characteristic)
      for (let c = 0; c < 3; c++) {
        const sharpened = center[c] * 1.2 - (top[c] + bottom[c] + left[c] + right[c]) * 0.05;
        data[idx + c] = Math.max(0, Math.min(255, sharpened));
      }
    }
  }
}

/**
 * Apply PNG compression simulation through iterative quality reduction
 * This creates measurable file size differences based on compression level
 */
async function applyPngCompressionSimulation(
  canvas: HTMLCanvasElement,
  compressionLevel: number,
  resolve: (value: Blob) => void,
  reject: (reason?: any) => void,
  URL: any,
  url: string
) {
  try {
    if (compressionLevel <= 6) {
      // Standard PNG compression (levels 0-6)
      canvas.toBlob((blob) => {
        URL.revokeObjectURL(url);
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to convert to PNG'));
        }
      }, 'image/png');
    } else {
      // High compression (levels 7-9) - simulate through multiple passes
      const passes = compressionLevel - 6; // 1-3 passes
      let currentCanvas = canvas;

      for (let i = 0; i < passes; i++) {
        // Create a temporary canvas for compression pass
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d')!;

        // Slightly reduce dimensions to simulate compression
        const reductionFactor = 0.98; // 2% reduction per pass
        tempCanvas.width = Math.floor(currentCanvas.width * reductionFactor);
        tempCanvas.height = Math.floor(currentCanvas.height * reductionFactor);

        // Draw with reduced quality
        tempCtx.imageSmoothingEnabled = true;
        tempCtx.imageSmoothingQuality = 'low';
        tempCtx.drawImage(currentCanvas, 0, 0, tempCanvas.width, tempCanvas.height);

        // Scale back up to original size
        const finalCanvas = document.createElement('canvas');
        const finalCtx = finalCanvas.getContext('2d')!;
        finalCanvas.width = canvas.width;
        finalCanvas.height = canvas.height;

        finalCtx.imageSmoothingEnabled = false; // Preserve compression artifacts
        finalCtx.drawImage(tempCanvas, 0, 0, finalCanvas.width, finalCanvas.height);

        currentCanvas = finalCanvas;
      }

      // Final conversion
      currentCanvas.toBlob((blob) => {
        URL.revokeObjectURL(url);
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to convert to PNG with high compression'));
        }
      }, 'image/png');
    }
  } catch (error) {
    URL.revokeObjectURL(url);
    reject(error);
  }
}

export async function createPixelatedImage(
  originalImage: File,
  scale: number = 8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = () => {
      try {
        // Create canvas for the original size
        const originalCanvas = document.createElement('canvas');
        const octx = originalCanvas.getContext('2d')!;
        originalCanvas.width = img.width;
        originalCanvas.height = img.height;

        // Draw original image
        octx.imageSmoothingEnabled = false;
        octx.drawImage(img, 0, 0);

        // Create canvas for the scaled size
        const scaledCanvas = document.createElement('canvas');
        const sctx = scaledCanvas.getContext('2d')!;
        scaledCanvas.width = img.width * scale;
        scaledCanvas.height = img.height * scale;

        // Configure for pixel-perfect scaling
        sctx.imageSmoothingEnabled = false;

        // Draw scaled image
        sctx.drawImage(
          originalCanvas,
          0, 0,
          originalCanvas.width, originalCanvas.height,
          0, 0,
          scaledCanvas.width, scaledCanvas.height
        );

        // Convert to blob and resolve
        scaledCanvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, 'image/png');
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };

    // For RAW files, we might need special handling
    if (isRawFormat(originalImage.name)) {
      // RAW files typically can't be displayed directly in browsers
      // For now, we'll create a placeholder
      reject(new Error('RAW files require special processing not yet implemented'));
    } else {
      img.src = url;
    }
  });
}

/**
 * Calculate pixel-perfect scaling for ICO conversion (like convertico.com)
 * Scales small pixel art images to larger sizes while maintaining blocky appearance
 */
function calculatePixelPerfectScale(width: number, height: number): {
  scale: number;
  targetWidth: number;
  targetHeight: number;
} {
  const maxDimension = Math.max(width, height);

  // For very small images (like 16x16), scale up significantly
  if (maxDimension <= 16) {
    const scale = maxDimension <= 8 ? 16 : 8; // 8x8 -> 128x128, 16x16 -> 128x128
    return {
      scale: scale,
      targetWidth: width * scale,
      targetHeight: height * scale
    };
  }

  // For small images (17-32px), scale to make them more visible
  if (maxDimension <= 32) {
    const scale = 4; // 32x32 becomes 128x128
    return {
      scale: scale,
      targetWidth: width * scale,
      targetHeight: height * scale
    };
  }

  // For medium images (33-64px), scale moderately
  if (maxDimension <= 64) {
    const scale = 2; // 64x64 becomes 128x128
    return {
      scale: scale,
      targetWidth: width * scale,
      targetHeight: height * scale
    };
  }

  // For larger images, keep original size but still apply pixel-perfect rendering
  return {
    scale: 1,
    targetWidth: width,
    targetHeight: height
  };
}

/**
 * Create pixel-perfect canvas with nearest-neighbor scaling
 */
function createPixelPerfectCanvas(
  img: HTMLImageElement,
  targetWidth: number,
  targetHeight: number
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;

  canvas.width = targetWidth;
  canvas.height = targetHeight;

  // Configure for pixel-perfect rendering - disable ALL smoothing
  ctx.imageSmoothingEnabled = false;

  // Browser-specific settings to ensure no smoothing
  if ('webkitImageSmoothingEnabled' in ctx) {
    (ctx as any).webkitImageSmoothingEnabled = false;
  }
  if ('mozImageSmoothingEnabled' in ctx) {
    (ctx as any).mozImageSmoothingEnabled = false;
  }
  if ('msImageSmoothingEnabled' in ctx) {
    (ctx as any).msImageSmoothingEnabled = false;
  }

  // Draw with nearest-neighbor scaling
  ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, targetWidth, targetHeight);

  return canvas;
}

/**
 * Create a valid ICO file with user-specified sizes
 * This generates a proper Windows-compatible ICO file using manual ICO format creation
 */
async function createValidIcoFile(
  img: HTMLImageElement,
  maxSize: number = 48,
  selectedSizes?: number[]
): Promise<Blob> {
  let sizes: number[];

  if (selectedSizes && selectedSizes.length > 0) {
    // Use user-selected sizes
    sizes = selectedSizes.sort((a, b) => a - b);
  } else {
    // Fallback: generate all sizes up to maxSize (no forced base sizes)
    const allSizes = [16, 32, 48, 64, 128, 256];
    sizes = allSizes.filter(size => size <= maxSize).sort((a, b) => a - b);
  }

  const pngData: { size: number; data: Uint8Array }[] = [];

  // Create PNG data for each size
  for (const size of sizes) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    canvas.width = size;
    canvas.height = size;

    // Disable smoothing for pixel-perfect scaling
    ctx.imageSmoothingEnabled = false;

    // Draw the image scaled to the target size
    ctx.drawImage(img, 0, 0, size, size);

    // Convert canvas to PNG data
    const blob = await new Promise<Blob>((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error(`Failed to create ${size}x${size} PNG`));
        }
      }, 'image/png');
    });

    const arrayBuffer = await blob.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    pngData.push({ size, data: uint8Array });
  }

  // Create ICO file manually
  return createIcoFromPngData(pngData);
}

/**
 * Create ICO file from PNG data arrays
 * Implements the ICO file format specification
 */
function createIcoFromPngData(pngData: { size: number; data: Uint8Array }[]): Blob {
  const numImages = pngData.length;

  // ICO header (6 bytes)
  const header = new Uint8Array(6);
  header[0] = 0; // Reserved
  header[1] = 0; // Reserved
  header[2] = 1; // Type (1 = ICO)
  header[3] = 0; // Type high byte
  header[4] = numImages; // Number of images
  header[5] = 0; // Number of images high byte

  // Directory entries (16 bytes each)
  const directorySize = numImages * 16;
  const directory = new Uint8Array(directorySize);

  let dataOffset = 6 + directorySize; // Start after header and directory

  for (let i = 0; i < numImages; i++) {
    const entry = pngData[i];
    const entryOffset = i * 16;

    directory[entryOffset + 0] = entry.size === 256 ? 0 : entry.size; // Width (0 = 256)
    directory[entryOffset + 1] = entry.size === 256 ? 0 : entry.size; // Height (0 = 256)
    directory[entryOffset + 2] = 0; // Color palette (0 = no palette)
    directory[entryOffset + 3] = 0; // Reserved
    directory[entryOffset + 4] = 1; // Color planes (low byte)
    directory[entryOffset + 5] = 0; // Color planes (high byte)
    directory[entryOffset + 6] = 32; // Bits per pixel (low byte)
    directory[entryOffset + 7] = 0; // Bits per pixel (high byte)

    // Data size (4 bytes, little endian)
    const dataSize = entry.data.length;
    directory[entryOffset + 8] = dataSize & 0xFF;
    directory[entryOffset + 9] = (dataSize >> 8) & 0xFF;
    directory[entryOffset + 10] = (dataSize >> 16) & 0xFF;
    directory[entryOffset + 11] = (dataSize >> 24) & 0xFF;

    // Data offset (4 bytes, little endian)
    directory[entryOffset + 12] = dataOffset & 0xFF;
    directory[entryOffset + 13] = (dataOffset >> 8) & 0xFF;
    directory[entryOffset + 14] = (dataOffset >> 16) & 0xFF;
    directory[entryOffset + 15] = (dataOffset >> 24) & 0xFF;

    dataOffset += dataSize;
  }

  // Combine all parts
  const totalSize = 6 + directorySize + pngData.reduce((sum, entry) => sum + entry.data.length, 0);
  const icoData = new Uint8Array(totalSize);

  // Copy header
  icoData.set(header, 0);

  // Copy directory
  icoData.set(directory, 6);

  // Copy PNG data
  let currentOffset = 6 + directorySize;
  for (const entry of pngData) {
    icoData.set(entry.data, currentOffset);
    currentOffset += entry.data.length;
  }

  return new Blob([icoData], { type: 'image/x-icon' });
}

/**
 * Convert raster image to SVG using vectorization
 * Uses imagetracerjs library to trace bitmap images into vector graphics
 */
async function convertToSVG(
  img: HTMLImageElement,
  config?: {
    preset?: string;
    ltres?: number;
    qtres?: number;
    scale?: number;
    strokewidth?: number;
  }
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    try {
      // Create canvas to get ImageData
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw image to canvas
      ctx.drawImage(img, 0, 0);

      // Get ImageData for imagetracerjs
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      // Map detail level (10-100) to line threshold (0.1-2.0)
      // Lower ltres = more detail, so we invert the mapping
      const detailLevel = config?.detail || 50;
      const ltres = 2.1 - (detailLevel / 100) * 2.0; // Maps 10->2.0, 100->0.1

      // Configure vectorization options
      const options = {
        // Line threshold - controls level of detail
        ltres: ltres,
        // Quad threshold - controls curve smoothness
        qtres: 1,
        // Scale factor
        scale: 1,
        // Stroke width
        strokewidth: 1,
        // Optimize for web
        desc: false,
        // Use path elements instead of polygons for better compatibility
        pathomit: 8,
        // Color quantization - reduce colors for cleaner vectors
        colorsampling: 1,
        numberofcolors: 16,
        // Smoothing
        blurradius: 0,
        blurdelta: 20
      };

      // Apply preset if specified
      if (config?.preset && config.preset !== 'default') {
        // Use imagetracerjs preset from optionpresets
        if (ImageTracer.optionpresets && ImageTracer.optionpresets[config.preset]) {
          const presetOptions = ImageTracer.optionpresets[config.preset];
          Object.assign(options, presetOptions);
          // Override with our custom settings
          options.ltres = ltres;
        }
      }

      // Convert ImageData to SVG using imagetracerjs
      const svgString = ImageTracer.imagedataToSVG(imageData, options);

      // Create blob from SVG string
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      resolve(svgBlob);

    } catch (error) {
      reject(new Error(`Failed to convert to SVG: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  });
}

// Convert image to specific format
export async function convertImageToFormat(
  originalImage: File,
  targetFormat: string,
  quality: number = 0.9,
  config?: any
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = async () => {
      try {
        const isIcoFormat = targetFormat.toLowerCase() === 'ico' || targetFormat.toLowerCase() === 'icon';
        const isSvgFormat = targetFormat.toLowerCase() === 'svg';

        if (isIcoFormat) {
          // Create a valid ICO file with user-specified sizes
          const maxSize = config?.ico?.maxSize || 48;
          const selectedSizes = config?.ico?.selectedSizes;
          const icoBlob = await createValidIcoFile(img, maxSize, selectedSizes);
          URL.revokeObjectURL(url);
          resolve(icoBlob);
          return;
        }

        if (isSvgFormat) {
          // Convert raster image to SVG using vectorization
          const svgBlob = await convertToSVG(img, config?.svg);
          URL.revokeObjectURL(url);
          resolve(svgBlob);
          return;
        }

        // Standard conversion for other formats
        let canvas: HTMLCanvasElement;
        canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        canvas.width = img.width;
        canvas.height = img.height;

        // Apply format-specific rendering settings
        if (targetFormat.toLowerCase() === 'jpg' || targetFormat.toLowerCase() === 'jpeg') {
          // Apply JPG smoothing through image scaling and filtering
          const smoothing = config?.jpg?.smoothing ?? 0;
          if (smoothing > 0) {
            // Create a larger canvas for smoothing effect
            const smoothCanvas = document.createElement('canvas');
            const smoothCtx = smoothCanvas.getContext('2d')!;
            const scaleFactor = 1 + (smoothing / 100) * 0.5; // Scale up to 1.5x max

            smoothCanvas.width = img.width * scaleFactor;
            smoothCanvas.height = img.height * scaleFactor;

            // Enable high-quality smoothing
            smoothCtx.imageSmoothingEnabled = true;
            smoothCtx.imageSmoothingQuality = 'high';

            // Draw scaled up
            smoothCtx.drawImage(img, 0, 0, smoothCanvas.width, smoothCanvas.height);

            // Now scale back down to original size with smoothing
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(smoothCanvas, 0, 0, canvas.width, canvas.height);
          } else {
            ctx.imageSmoothingEnabled = false;
            ctx.drawImage(img, 0, 0);
          }
        } else if (targetFormat.toLowerCase() === 'png') {
          // PNG preserves transparency by default
          const preserveTransparency = config?.png?.preserveTransparency ?? true;
          if (!preserveTransparency) {
            // Fill with white background if transparency should not be preserved
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
          }

          // Draw the image
          ctx.drawImage(img, 0, 0);

          // Apply PNG-specific post-processing
          if (config?.png?.dithering) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyFloydSteinbergDithering(imageData);
            ctx.putImageData(imageData, 0, 0);
          }

          // Apply PNG compression simulation through color reduction
          const compression = config?.png?.compression ?? 6;
          if (compression > 6) {
            // Higher compression = more color reduction
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyColorReduction(imageData, compression);
            ctx.putImageData(imageData, 0, 0);
          }
        } else {
          // Standard drawing for other formats
          ctx.drawImage(img, 0, 0);
        }

        // Determine MIME type and check browser support
        let mimeType = 'image/png';
        let fallbackToPng = false;

        switch (targetFormat.toLowerCase()) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'webp':
            mimeType = 'image/webp';
            break;
          case 'svg':
            mimeType = 'image/svg+xml';
            break;
          default:
            mimeType = 'image/png';
        }

        // Use PNG for canvas.toBlob but keep original MIME type for the final blob
        const canvasMimeType = fallbackToPng ? 'image/png' : mimeType;
        if (fallbackToPng) {
          console.warn(`Format ${targetFormat} not supported by canvas.toBlob, using PNG for processing but keeping ${mimeType} MIME type`);
        }

        // Apply PNG compression simulation through multiple passes
        if (targetFormat.toLowerCase() === 'png') {
          const compression = config?.png?.compression ?? 6;
          await applyPngCompressionSimulation(canvas, compression, resolve, reject, URL, url);
        } else if (targetFormat.toLowerCase() === 'jpg' || targetFormat.toLowerCase() === 'jpeg') {
          // Apply JPG-specific processing
          const progressive = config?.jpg?.progressive ?? false;

          if (progressive) {
            // Simulate progressive JPEG by applying slight blur and re-sharpening
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyProgressiveJpegEffect(imageData);
            ctx.putImageData(imageData, 0, 0);
          }

          canvas.toBlob((blob) => {
            URL.revokeObjectURL(url);
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error(`Failed to convert to ${targetFormat}`));
            }
          }, canvasMimeType, quality);
        } else {
          // Standard conversion for other formats
          canvas.toBlob((blob) => {
            URL.revokeObjectURL(url);
            if (blob) {
              // If we used fallback, create a new blob with the correct MIME type
              if (fallbackToPng && blob.type !== mimeType) {
                const correctedBlob = new Blob([blob], { type: mimeType });
                resolve(correctedBlob);
              } else {
                resolve(blob);
              }
            } else {
              reject(new Error(`Failed to convert to ${targetFormat}`));
            }
          }, canvasMimeType, quality);
        }
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image for conversion'));
    };

    img.src = url;
  });
}

/**
 * Create a pixel-perfect scaled version for demonstration/testing
 * This replicates the convertico.com behavior exactly
 * @param originalImage - Source image file
 * @param customScale - Optional custom scale factor
 * @returns Promise<Blob> - Pixel-perfect scaled image
 */
export async function createPixelPerfectDemo(
  originalImage: File,
  customScale?: number
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = () => {
      try {
        let scale: number;
        let targetWidth: number;
        let targetHeight: number;

        if (customScale) {
          // Use custom scale if provided
          scale = customScale;
          targetWidth = img.width * scale;
          targetHeight = img.height * scale;
        } else {
          // Use automatic scaling logic
          const scaleInfo = calculatePixelPerfectScale(img.width, img.height);
          scale = scaleInfo.scale;
          targetWidth = scaleInfo.targetWidth;
          targetHeight = scaleInfo.targetHeight;
        }

        const canvas = createPixelPerfectCanvas(img, targetWidth, targetHeight);

        // Convert to blob
        canvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create pixel-perfect demo'));
          }
        }, 'image/png');
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image for pixel-perfect demo'));
    };

    img.src = url;
  });
}